// Game elements
const canvas = document.getElementById("gameCanvas");
const ctx = canvas.getContext("2d");
const messageDiv = document.getElementById("message");
const closeButton = document.getElementById("close-message");

// Game images
const images = {
    background: new Image(),
    girlWalk: new Image(),
    dorji: new Image(),
    key: new Image(),
    lamp: new Image()
};

// Set image sources
images.background.src = "landchorten.png";
images.girlWalk.src = "girlwalk.png";
images.dorji.src = "dorji.png";
images.key.src = "key.png";
images.lamp.src = "lamp.png";

// Game objects
const girl = {
    x: -100,
    y: 470,
    width: 80,
    height: 190,
    speed: 3,
    stopX: 1000,
    hasStopped: false
};

const treasures = [
    { x: 1090, y: 450, width: 50, height: 50, type: "dorji", bounce: 0, bounceSpeed: 0.05 },
    { x: 1090, y: 500, width: 50, height: 50, type: "key", bounce: 0, bounceSpeed: 0.07 },
    { x: 1090, y: 390, width: 50, height: 50, type: "lamp", bounce: 0, bounceSpeed: 0.06 }
];

// Game state
let assetsLoaded = 0;
const totalAssets = Object.keys(images).length;

// Initialize game when all images are loaded
Object.values(images).forEach(img => {
    img.onload = () => {
        assetsLoaded++;
        if (assetsLoaded === totalAssets) {
            initGame();
        }
    };
});

function initGame() {
    canvas.width = images.background.width;
    canvas.height = images.background.height;
    gameLoop();
}

function drawGirl() {
    // Smooth walking with no vertical movement
    ctx.drawImage(
        images.girlWalk, 
        girl.x, 
        girl.y, 
        girl.width, 
        girl.height
    );
}

function drawTreasures() {
    treasures.forEach(treasure => {
        const img = images[treasure.type];
        
        // Update bounce position
        treasure.bounce += treasure.bounceSpeed;
        const bounceHeight = Math.sin(treasure.bounce) * 7;
        
        // Draw treasure with bounce effect
        ctx.drawImage(
            img,
            treasure.x,
            treasure.y + bounceHeight,
            treasure.width,
            treasure.height
        );
        
        // Glitter effect (sparkles)
        if (Math.random() > 0.9) {
            ctx.save();
            ctx.globalAlpha = 0.7;
            ctx.fillStyle = `rgba(255, 255, 255, ${Math.random() * 0.8})`;
            ctx.beginPath();
            ctx.arc(
                treasure.x + Math.random() * treasure.width,
                treasure.y + Math.random() * treasure.height + bounceHeight,
                Math.random() * 4 + 2,
                0,
                Math.PI * 2
            );
            ctx.fill();
            ctx.restore();
        }
    });
}

function update() {
    // Move girl until stopX is reached
    if (girl.x < girl.stopX) {
        girl.x += girl.speed;
    }
    // Show message when stopped
    else if (!girl.hasStopped) {
        girl.hasStopped = true;
        setTimeout(() => {
            messageDiv.style.display = "block";
        }, 500);
    }

    // Update treasure bounce animations
    treasures.forEach(treasure => {
        treasure.bounce += treasure.bounceSpeed;
    });
}

function gameLoop() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    ctx.drawImage(images.background, 0, 0, canvas.width, canvas.height);
    
    // Draw game elements
    drawTreasures();
    drawGirl();
    
    update();
    requestAnimationFrame(gameLoop);
}

// Close message button - dramatic black fade then redirect to new chorten page
closeButton.addEventListener("click", () => {
    messageDiv.style.display = "none";

    // Create black fade overlay for dramatic effect
    const fadeOverlay = document.createElement('div');
    fadeOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        opacity: 0;
        z-index: 1000;
        transition: opacity 2s ease-in-out;
    `;
    document.body.appendChild(fadeOverlay);

    // Slow fade in
    setTimeout(() => {
        fadeOverlay.style.opacity = '1';
    }, 100);

    // Stay black for dramatic pause, then redirect
    setTimeout(() => {
        window.location.href = "newchorten.html";
    }, 4000); // 2s fade in + 2s stay black
});